{"name": "app_frontend_agentq", "version": "1.0.0", "description": "AgentQ Frontend Microservice", "private": true, "scripts": {"dev": "vite", "prebuild": "rm -rf dist && rm -rf tsconfig.app.tsbuildinfo && rm -rf tsconfig.node.tsbuildinfo", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "chart.js": "^4.4.8", "pinia": "^3.0.1", "socket.io-client": "^4.8.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.3.1", "@types/socket.io-client": "^1.4.36", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^5.1.3", "eslint": "^8.42.0", "prettier": "^3.0.0", "sass": "^1.69.5", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}