// src/types/index.ts

export interface Project {
    id: string;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    createdBy?: string;
  }
  
export interface Tag {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  }
  
export interface TestCase {
    id: string;
    projectId: string;
    tcId: number;
    title: string;
    type: string;
    priority: string;
    platform: string;
    testCaseType: string;
    precondition: string;
    steps: string;
    expectation: string;
    folderId: string | null;
    tags: Tag[];
    createdAt: string;
    updatedAt: string;
    automationByAgentq?: boolean;
  }
  
export interface Folder {
    id: string;
    name: string;
    parentId?: string | null;
    children?: Folder[];
    createdAt: string;
    updatedAt: string;
  }

export interface Requirement {
  id: string;
  projectId: string;
  requirement_id: string;
  name: string;
  status: 'draft' | 'reviewed_by_ai' | 'revised' | 'approved' | 'published' | 'rejected';
  description?: string;
  file_name?: string;
  file_type?: string;
  file_size?: number;
  uploadedAt: string;
  created_at: string;
  updatedAt: string;
  createdBy?: string;
  reviewedBy?: string;
  reviewedAt?: string;
}

// Async upload related types
export enum JobStatus {
  QUEUED = 'queued',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum JobStep {
  VALIDATING = 'validating',
  UPLOADING_TO_STORAGE = 'uploading_to_storage',
  PROCESSING_CONTENT = 'processing_content',
  SAVING_TO_DATABASE = 'saving_to_database',
  GENERATING_ANALYSIS = 'generating_analysis',
  COMPLETED = 'completed'
}

export interface JobProgress {
  currentStep: JobStep;
  percentage: number;
  description: string;
  updatedAt: Date;
}

export interface JobStatusResponse {
  jobId: string;
  status: JobStatus;
  progress: JobProgress;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
  result?: {
    id: string;
    requirement_id: string;
    status: string;
    storageUrl: string;
  };
}

export interface UploadJobResponse {
  jobId: string;
  status: string;
  message: string;
  estimatedProcessingTime: number;
}

export interface UploadJob {
  id: string;
  fileName: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  error?: string;
}

export interface Profile {
  company: {
    subscription: {
      name: string;
      tokenLimit: number;
      remainingTokens: number;
      isEnterprise: boolean;
      endDate: string;
    };
  };
}
