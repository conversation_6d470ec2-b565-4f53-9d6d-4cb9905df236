import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from './user.entity';
import { RequirementStatus } from './requirement-status.entity';
import { RequirementRevision } from './requirement-revision.entity';
import { AiAnalysis } from './ai-analysis.entity';
import { TokenUsage } from './token-usage.entity';
import { VectorTransformer } from '../database/types/vector.type';

export enum ProcessingMethod {
  TRADITIONAL_PARSE = 'traditional_parse',
  EMBEDDING_BASED = 'embedding_based',
  HYBRID = 'hybrid'
}

export enum EmbeddingStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

@Entity('requirements')
export class Requirement {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50, unique: true })
  requirementId: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'int', name: 'status_id' })
  statusId: number;

  @Column({ type: 'varchar', length: 500, name: 'storage_url', nullable: true })
  storageUrl: string;

  @Column({ type: 'bigint', name: 'uploaded_by', nullable: true })
  uploadedById: number;

  // Vector embedding for semantic search (using pgvector extension)
  @Column({
    type: 'text', // TypeORM doesn't recognize 'vector' type, but we'll store as text and use transformer
    transformer: new VectorTransformer(),
    nullable: true,
    name: 'embedding_vector'
  })
  embeddingVector: number[]; // pgvector type

  // Metadata for processing information
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => RequirementStatus, status => status.requirements)
  @JoinColumn({ name: 'status_id' })
  status: RequirementStatus;

  @ManyToOne(() => User, user => user.requirements)
  @JoinColumn({ name: 'uploaded_by' })
  uploadedBy: User;

  @OneToMany(() => RequirementRevision, revision => revision.requirement)
  revisions: RequirementRevision[];

  @OneToMany(() => AiAnalysis, analysis => analysis.requirement)
  analyses: AiAnalysis[];

  @OneToMany(() => TokenUsage, tokenUsage => tokenUsage.requirement)
  tokenUsages: TokenUsage[];
}
