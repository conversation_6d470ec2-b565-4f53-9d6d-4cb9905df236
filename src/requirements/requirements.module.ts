import { Module, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { RequirementsController } from './requirements.controller';
import { RequirementsService } from './requirements.service';
import { RequirementStatusSeederService } from './requirement-status-seeder.service';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';
import { DatabaseTestService } from './database-test.service';
import { UploadQueueService } from './services/upload-queue.service';
import { BulkDeleteQueueService } from './services/bulk-delete-queue.service';
import { UploadProcessor } from './processors/upload.processor';
import { BulkDeleteProcessor } from './processors/bulk-delete.processor';
import { UploadProgressGateway } from './gateways/upload-progress.gateway';
import {
  User,
  RequirementStatus,
  Requirement,
  RequirementRevision,
  AiAnalysis
} from '../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      RequirementStatus,
      Requirement,
      RequirementRevision,
      AiAnalysis,
    ]),
    BullModule.registerQueue({
      name: 'upload-queue',
    }),
    BullModule.registerQueue({
      name: 'bulk-delete-queue',
    }),
  ],
  controllers: [RequirementsController],
  providers: [
    RequirementsService,
    RequirementStatusSeederService,
    GoogleCloudStorageService,
    DatabaseTestService,
    UploadQueueService,
    BulkDeleteQueueService,
    UploadProcessor,
    BulkDeleteProcessor,
    UploadProgressGateway,
    {
      provide: 'UploadProgressGateway',
      useExisting: UploadProgressGateway,
    },
  ],
  exports: [RequirementsService, UploadQueueService, BulkDeleteQueueService],
})
export class RequirementsModule implements OnModuleInit {
  constructor(private readonly seederService: RequirementStatusSeederService) {}

  async onModuleInit() {
    await this.seederService.seed();
  }
}
