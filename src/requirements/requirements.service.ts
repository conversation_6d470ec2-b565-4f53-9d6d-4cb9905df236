import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, User, RequirementStatus } from '../entities';
import { UploadRequirementDto, QueryRequirementsDto, UploadJobResponseDto, BulkDeleteRequirementsDto, BulkDeleteJobResponseDto } from './dto';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';
import { UploadQueueService } from './services/upload-queue.service';
import { BulkDeleteQueueService } from './services/bulk-delete-queue.service';

@Injectable()
export class RequirementsService {
  constructor(
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(RequirementStatus)
    private requirementStatusRepository: Repository<RequirementStatus>,
    private googleCloudStorageService: GoogleCloudStorageService,
    private uploadQueueService: UploadQueueService,
    private bulkDeleteQueueService: BulkDeleteQueueService,
  ) {}

  // New async upload method
  async uploadRequirementAsync(
    uploadDto: UploadRequirementDto,
    fileContent: string,
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
  ): Promise<UploadJobResponseDto> {
    // Add job to queue and return immediately
    const jobId = await this.uploadQueueService.addUploadJob(
      uploadDto,
      fileBuffer,
      originalFilename,
      mimetype,
      fileContent
    );

    return {
      jobId,
      status: 'queued',
      message: 'Upload job created successfully. Use jobId to track progress.',
      estimatedProcessingTime: 30 // seconds
    };
  }

  // Original synchronous method (kept for backward compatibility)
  async uploadRequirement(
    uploadDto: UploadRequirementDto,
    fileContent: string,
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
  ): Promise<{ id: string; requirement_id: string; status: string; message: string }> {
    // Find or create user
    let user = await this.userRepository.findOne({
      where: { userId: uploadDto.user_id }
    });

    if (!user) {
      user = this.userRepository.create({
        userId: uploadDto.user_id,
        companyId: uploadDto.company_id,
        projectId: uploadDto.project_id,
      });
      await this.userRepository.save(user);
    }

    // Get draft status ID
    const draftStatus = await this.requirementStatusRepository.findOne({
      where: { name: 'draft' }
    });

    if (!draftStatus) {
      throw new Error('Draft status not found. Please ensure requirement statuses are seeded.');
    }

    // Generate requirement ID (simple counter-based for now)
    const count = await this.requirementRepository.count();
    const requirementId = `R-${String(count + 1).padStart(3, '0')}`;

    // Upload file to Google Cloud Storage
    const storageUrl = await this.googleCloudStorageService.uploadRequirementFile(
      fileBuffer,
      originalFilename,
      mimetype,
      requirementId,
    );

    // Create requirement
    const requirement = this.requirementRepository.create({
      requirementId: requirementId,
      name: uploadDto.name,
      content: fileContent,
      statusId: draftStatus.id,
      storageUrl,
      uploadedById: user.id,
    });

    const savedRequirement = await this.requirementRepository.save(requirement);

    return {
      id: savedRequirement.id,
      requirement_id: savedRequirement.requirementId,
      status: 'draft',
      message: 'File uploaded successfully and is being processed asynchronously'
    };
  }

  async findAll(queryDto: QueryRequirementsDto): Promise<{
    data: any[];
    pagination: { page: number; limit: number; total: number; totalPages: number };
  }> {
    try {
      const {
        status,
        project_id,
        sort_by = 'created_at',
        order = 'DESC',
        page = 1,
        limit = 10
      } = queryDto;

    // Use find with relations instead of query builder to avoid join issues
    let relations = ['status', 'uploadedBy'];

    // Map sort field names to entity property names
    const sortFieldMap: { [key: string]: string } = {
      'created_at': 'createdAt',
      'updated_at': 'updatedAt',
      'status': 'statusId'
    };

    const actualSortField = sortFieldMap[sort_by] || sort_by;

    let requirements = await this.requirementRepository.find({
      relations,
      order: {
        [actualSortField]: order as 'ASC' | 'DESC'
      }
    });

    // Apply filters after loading (since join conditions were problematic)
    if (status) {
      requirements = requirements.filter(req => req.status?.name === status);
    }

    if (project_id) {
      requirements = requirements.filter(req => req.uploadedBy?.projectId === project_id);
    }

    const total = requirements.length;

    // Apply pagination
    const offset = (page - 1) * limit;
    requirements = requirements.slice(offset, offset + limit);

    const data = requirements.map(req => ({
      id: req.id,
      requirement_id: req.requirementId,
      name: req.name,
      status: req.status?.name,
      storage_url: req.storageUrl,
      uploaded_by: req.uploadedBy?.userId,
      created_at: req.createdAt,
      updated_at: req.updatedAt,
    }));

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error in findAll:', error);
      // Return empty result instead of throwing
      return {
        data: [],
        pagination: {
          page: queryDto.page || 1,
          limit: queryDto.limit || 10,
          total: 0,
          totalPages: 0,
        },
      };
    }
  }

  async findOne(id: string): Promise<any> {
    const requirement = await this.requirementRepository.findOne({
      where: { id },
      relations: ['status', 'uploadedBy'],
    });

    if (!requirement) {
      throw new NotFoundException(`Requirement with ID ${id} not found`);
    }

    return {
      id: requirement.id,
      requirement_id: requirement.requirementId,
      name: requirement.name,
      content: requirement.content,
      status: requirement.status?.name,
      storage_url: requirement.storageUrl,
      uploaded_by: requirement.uploadedBy?.userId,
      created_at: requirement.createdAt,
      updated_at: requirement.updatedAt,
    };
  }

  // Bulk delete requirements asynchronously
  async bulkDeleteRequirementsAsync(
    bulkDeleteDto: BulkDeleteRequirementsDto,
  ): Promise<BulkDeleteJobResponseDto> {
    // Add job to queue and return immediately
    const jobId = await this.bulkDeleteQueueService.addBulkDeleteJob(bulkDeleteDto);

    return {
      jobId,
      status: 'queued',
      message: 'Bulk delete job created successfully. Use jobId to track progress.',
      totalRequirements: bulkDeleteDto.requirementIds.length,
      estimatedProcessingTime: Math.max(30, bulkDeleteDto.requirementIds.length * 5) // 5 seconds per requirement, minimum 30 seconds
    };
  }


}
