import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TokenUsage, TokenType } from '../../entities';

interface LLMValidationResult {
  canUseTraditionalParse: boolean;
  confidence: number;
  reasoning: string;
  detectedElements: string[];
  hasImages: boolean;
  hasDiagrams: boolean;
  hasComplexLayout: boolean;
  tokensUsed: number;
}

@Injectable()
export class LLMValidationService {
  private readonly logger = new Logger(LLMValidationService.name);
  private readonly llmProvider: string;

  constructor(
    private configService: ConfigService,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
  ) {
    this.llmProvider = this.configService.get<string>('LLM', 'GEMINI');
  }

  async validateFileForTraditionalParsing(
    fileBuffer: Buffer,
    mimetype: string,
    originalFilename: string,
    requirementId: string
  ): Promise<LLMValidationResult> {
    this.logger.log(`Validating file ${originalFilename} for traditional parsing capability`);

    try {
      // Prepare file analysis prompt
      const analysisPrompt = this.buildAnalysisPrompt(mimetype, originalFilename);
      
      let result: LLMValidationResult;

      if (this.llmProvider === 'OPENAI') {
        result = await this.validateWithOpenAI(fileBuffer, analysisPrompt, mimetype);
      } else if (this.llmProvider === 'GEMINI') {
        result = await this.validateWithGemini(fileBuffer, analysisPrompt, mimetype);
      } else {
        throw new Error(`Unsupported LLM provider: ${this.llmProvider}`);
      }

      // Record token usage
      await this.recordTokenUsage(requirementId, result.tokensUsed, 'llm_validation');

      this.logger.log(`Validation completed for ${originalFilename}: ${result.canUseTraditionalParse ? 'Traditional parsing suitable' : 'Embedding required'}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Error validating file ${originalFilename}:`, error);
      
      // Fallback: assume traditional parsing won't work for complex files
      const fallbackResult: LLMValidationResult = {
        canUseTraditionalParse: this.isSafeForTraditionalParsing(mimetype),
        confidence: 0.5,
        reasoning: `Validation failed, using fallback logic based on file type: ${mimetype}`,
        detectedElements: ['unknown'],
        hasImages: false,
        hasDiagrams: false,
        hasComplexLayout: false,
        tokensUsed: 0
      };

      return fallbackResult;
    }
  }

  private buildAnalysisPrompt(mimetype: string, filename: string): string {
    return `
Analyze this ${mimetype} file (${filename}) and determine if traditional text parsing methods can extract meaningful content, or if advanced embedding-based processing is needed.

Consider these factors:
1. Does the file contain images, diagrams, charts, or visual elements that carry important information?
2. Is the layout complex with multiple columns, tables, or non-linear text flow?
3. Are there embedded objects, annotations, or interactive elements?
4. Is the text clearly structured and readable through standard parsing?

Respond with a JSON object containing:
{
  "canUseTraditionalParse": boolean,
  "confidence": number (0-1),
  "reasoning": "detailed explanation",
  "detectedElements": ["list", "of", "detected", "elements"],
  "hasImages": boolean,
  "hasDiagrams": boolean,
  "hasComplexLayout": boolean
}

Be conservative - if there's any doubt about traditional parsing effectiveness, recommend embedding-based processing.
    `.trim();
  }

  private async validateWithOpenAI(
    fileBuffer: Buffer,
    prompt: string,
    mimetype: string
  ): Promise<LLMValidationResult> {
    // Note: This is a placeholder implementation
    // You'll need to implement actual OpenAI API calls based on your needs
    
    const { Configuration, OpenAIApi } = require('openai');
    
    const configuration = new Configuration({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
    const openai = new OpenAIApi(configuration);

    try {
      // For text files, analyze content directly
      if (mimetype.includes('text/') || mimetype.includes('application/json')) {
        const content = fileBuffer.toString('utf-8');
        const response = await openai.createChatCompletion({
          model: 'gpt-4',
          messages: [
            { role: 'system', content: prompt },
            { role: 'user', content: `File content: ${content.substring(0, 4000)}...` }
          ],
          max_tokens: 500,
          temperature: 0.1,
        });

        const result = JSON.parse(response.data.choices[0].message?.content || '{}');
        return {
          ...result,
          tokensUsed: response.data.usage?.total_tokens || 0
        };
      }

      // For other file types, use vision API if available or fallback
      return this.getFallbackResult(mimetype);

    } catch (error) {
      this.logger.error('OpenAI validation error:', error);
      return this.getFallbackResult(mimetype);
    }
  }

  private async validateWithGemini(
    fileBuffer: Buffer,
    prompt: string,
    mimetype: string
  ): Promise<LLMValidationResult> {
    // Note: This is a placeholder implementation
    // You'll need to implement actual Gemini API calls
    
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    
    const genAI = new GoogleGenerativeAI(this.configService.get<string>('GEMINI_API_KEY'));
    
    try {
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

      // For text files
      if (mimetype.includes('text/') || mimetype.includes('application/json')) {
        const content = fileBuffer.toString('utf-8');
        const result = await model.generateContent([
          prompt,
          `File content: ${content.substring(0, 4000)}...`
        ]);

        const response = await result.response;
        const text = response.text();
        
        // Parse JSON response
        const parsed = JSON.parse(text);
        
        return {
          ...parsed,
          tokensUsed: response.usageMetadata?.totalTokenCount || 0
        };
      }

      // For files with potential visual content, use Gemini Pro Vision
      if (mimetype.includes('pdf') || mimetype.includes('image/')) {
        const visionModel = genAI.getGenerativeModel({ model: 'gemini-pro-vision' });
        
        // Convert buffer to base64 for Gemini
        const base64Data = fileBuffer.toString('base64');
        
        const result = await visionModel.generateContent([
          prompt,
          {
            inlineData: {
              data: base64Data,
              mimeType: mimetype
            }
          }
        ]);

        const response = await result.response;
        const text = response.text();
        const parsed = JSON.parse(text);
        
        return {
          ...parsed,
          tokensUsed: response.usageMetadata?.totalTokenCount || 0
        };
      }

      return this.getFallbackResult(mimetype);

    } catch (error) {
      this.logger.error('Gemini validation error:', error);
      return this.getFallbackResult(mimetype);
    }
  }

  private getFallbackResult(mimetype: string): LLMValidationResult {
    const canUseTraditional = this.isSafeForTraditionalParsing(mimetype);
    
    return {
      canUseTraditionalParse: canUseTraditional,
      confidence: 0.7,
      reasoning: `Fallback decision based on file type ${mimetype}`,
      detectedElements: [mimetype],
      hasImages: mimetype.includes('pdf') || mimetype.includes('image/'),
      hasDiagrams: mimetype.includes('pdf'),
      hasComplexLayout: mimetype.includes('pdf') || mimetype.includes('docx'),
      tokensUsed: 0
    };
  }

  private isSafeForTraditionalParsing(mimetype: string): boolean {
    const safeTypes = [
      'text/plain',
      'text/markdown',
      'text/csv',
      'application/json',
      'text/html'
    ];
    
    return safeTypes.includes(mimetype);
  }

  private async recordTokenUsage(
    requirementId: string,
    tokensUsed: number,
    tokenType: string
  ): Promise<void> {
    if (tokensUsed > 0) {
      const tokenUsage = this.tokenUsageRepository.create({
        requirementId,
        tokenType: tokenType as TokenType,
        tokensUsed,
        model: this.llmProvider === 'OPENAI' ? 'gpt-4' : 'gemini-pro',
        provider: this.llmProvider.toLowerCase(),
        cost: this.calculateCost(tokensUsed, this.llmProvider),
        metadata: {
          operation: 'file_validation',
          timestamp: new Date()
        }
      });

      await this.tokenUsageRepository.save(tokenUsage);
    }
  }

  private calculateCost(tokens: number, provider: string): number {
    // Rough cost calculation - adjust based on actual pricing
    const costPerToken = provider === 'OPENAI' ? 0.00003 : 0.00001; // USD per token
    return tokens * costPerToken;
  }
}
