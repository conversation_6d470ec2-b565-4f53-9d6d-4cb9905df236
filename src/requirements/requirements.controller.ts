import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Query,
  Param,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpStatus,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { RequirementsService } from './requirements.service';
import { DatabaseTestService } from './database-test.service';
import { UploadQueueService } from './services/upload-queue.service';
import {
  UploadRequirementDto,
  QueryRequirementsDto,
  UploadResponseDto,
  PaginatedRequirementsResponseDto,
  RequirementResponseDto,
  UploadJobResponseDto,
  JobStatusResponseDto,
} from './dto';

@ApiTags('requirements')
@Controller('api/v1/requirements')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RequirementsController {
  constructor(
    private readonly requirementsService: RequirementsService,
    private readonly databaseTestService: DatabaseTestService,
    private readonly uploadQueueService: UploadQueueService,
  ) {}

  @Post('upload/async')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload requirement file asynchronously' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload requirement file with metadata',
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The requirement file to upload'
        },
        name: {
          type: 'string',
          description: 'Name of the requirement'
        },
        user_id: {
          type: 'string',
          description: 'ID of the user uploading the file'
        },
        company_id: {
          type: 'string',
          description: 'ID of the company'
        },
        project_id: {
          type: 'string',
          description: 'ID of the project'
        }
      },
      required: ['file', 'name', 'user_id', 'company_id', 'project_id']
    }
  })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Upload job created successfully',
    type: UploadJobResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing required fields',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async uploadAsync(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadRequirementDto,
  ): Promise<UploadJobResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Extract file content based on file type
    let fileContent: string;
    try {
      if (file.mimetype === 'application/pdf') {
        const pdfParse = require('pdf-parse');
        const data = await pdfParse(file.buffer);
        fileContent = data.text;
      } else if (file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        const mammoth = require('mammoth');
        const result = await mammoth.extractRawText({ buffer: file.buffer });
        fileContent = result.value;
      } else if (file.mimetype === 'text/plain') {
        fileContent = file.buffer.toString('utf-8');
      } else {
        throw new BadRequestException('Unsupported file type. Please upload PDF, DOCX, or TXT files.');
      }
    } catch (error) {
      throw new BadRequestException('Failed to extract content from file');
    }

    return this.requirementsService.uploadRequirementAsync(
      uploadDto,
      fileContent,
      file.buffer,
      file.originalname,
      file.mimetype,
    );
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a new requirement document' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'File uploaded successfully and is being processed asynchronously',
    type: UploadResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing required fields',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async uploadRequirement(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadRequirementDto,
  ): Promise<UploadResponseDto> {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    // Validate file type
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown',
      'text/html',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Unsupported file type. Supported types: PDF, Word, Text, Markdown, HTML, Excel, CSV'
      );
    }

    // Extract text content from file
    const fileContent = this.extractTextFromFile(file.buffer, file.mimetype, file.originalname);

    const result = await this.requirementsService.uploadRequirement(
      uploadDto,
      fileContent,
      file.buffer,
      file.originalname,
      file.mimetype,
    );

    return result;
  }

  @Get()
  @ApiOperation({ summary: 'List all requirements with filtering and pagination' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by requirement status' })
  @ApiQuery({ name: 'project_id', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'sort_by', required: false, description: 'Sort by field' })
  @ApiQuery({ name: 'order', required: false, description: 'Sort order (ASC/DESC)' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of requirements retrieved successfully',
    type: PaginatedRequirementsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async findAll(@Query() queryDto: QueryRequirementsDto): Promise<PaginatedRequirementsResponseDto> {
    return this.requirementsService.findAll(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific requirement by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Requirement retrieved successfully',
    type: RequirementResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Requirement not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async findOne(@Param('id') id: string): Promise<RequirementResponseDto> {
    return this.requirementsService.findOne(id);
  }

  @Get('jobs/:jobId/status')
  @ApiOperation({ summary: 'Get upload job status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job status retrieved successfully',
    type: JobStatusResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getJobStatus(@Param('jobId') jobId: string): Promise<JobStatusResponseDto> {
    const status = await this.uploadQueueService.getJobStatus(jobId);
    if (!status) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    return status;
  }

  @Delete('jobs/:jobId')
  @ApiOperation({ summary: 'Cancel upload job' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job cancelled successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async cancelJob(@Param('jobId') jobId: string): Promise<{ message: string }> {
    const cancelled = await this.uploadQueueService.cancelJob(jobId);
    if (!cancelled) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
    return { message: 'Job cancelled successfully' };
  }

  @Post('jobs/:jobId/retry')
  @ApiOperation({ summary: 'Retry failed upload job' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job retried successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async retryJob(@Param('jobId') jobId: string): Promise<{ message: string }> {
    const retried = await this.uploadQueueService.retryFailedJob(jobId);
    if (!retried) {
      throw new NotFoundException(`Job with ID ${jobId} not found or cannot be retried`);
    }
    return { message: 'Job retried successfully' };
  }

  @Get('queue/stats')
  @ApiOperation({ summary: 'Get upload queue statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue statistics retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getQueueStats() {
    return this.uploadQueueService.getQueueStats();
  }



  private extractTextFromFile(buffer: Buffer, mimetype: string, originalname: string): string {
    // For now, return a simple text representation
    // In a real implementation, you would use libraries like:
    // - mammoth for .docx files
    // - pdf-parse for PDF files
    // - xlsx for Excel files
    // etc.
    
    if (mimetype.includes('text/') || originalname.endsWith('.md') || originalname.endsWith('.txt')) {
      return buffer.toString('utf-8');
    }
    
    // For other file types, return a placeholder
    return `[File content extracted from ${originalname}]\n\nThis is a placeholder for the actual file content that would be extracted using appropriate libraries for ${mimetype} files.`;
  }
}
