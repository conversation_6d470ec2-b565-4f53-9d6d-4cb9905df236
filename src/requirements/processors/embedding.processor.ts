import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { EmbeddingService } from '../services/embedding.service';
import { EmbeddingJobData, EmbeddingJobResult } from '../services/embedding-queue.service';

@Injectable()
@Processor('embeddings')
export class EmbeddingProcessor extends WorkerHost {
  private readonly logger = new Logger(EmbeddingProcessor.name);

  constructor(
    private embeddingService: EmbeddingService,
  ) {
    super();
  }

  async process(job: Job<EmbeddingJobData>): Promise<EmbeddingJobResult> {
    const { requirementId, content, additionalContext, jobId } = job.data;
    
    this.logger.log(`Processing embedding job ${jobId} for requirement ${requirementId}`);

    try {
      // Update progress: Starting
      await job.updateProgress(10);

      // Generate embedding
      this.logger.log(`Generating embedding for requirement ${requirementId}`);
      await job.updateProgress(30);

      const embeddingResult = await this.embeddingService.generateEmbedding(
        content,
        requirementId,
        additionalContext
      );

      await job.updateProgress(70);

      // Update requirement with embedding
      this.logger.log(`Updating requirement ${requirementId} with embedding`);
      await this.embeddingService.updateRequirementEmbedding(requirementId, embeddingResult);

      await job.updateProgress(100);

      const result: EmbeddingJobResult = {
        requirementId,
        success: true,
        embeddingGenerated: true,
        tokensUsed: embeddingResult.tokensUsed,
        message: `Embedding generated successfully using ${embeddingResult.provider}/${embeddingResult.model}`
      };

      this.logger.log(`Embedding job ${jobId} completed successfully`);
      return result;

    } catch (error) {
      this.logger.error(`Embedding job ${jobId} failed:`, error);
      
      const result: EmbeddingJobResult = {
        requirementId,
        success: false,
        embeddingGenerated: false,
        tokensUsed: 0,
        message: 'Embedding generation failed',
        error: error.message
      };

      throw error; // This will mark the job as failed
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`Embedding job ${job.id} completed successfully`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(`Embedding job ${job.id} failed:`, err);
  }

  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: number) {
    this.logger.log(`Embedding job ${job.id} progress: ${progress}%`);
  }
}
