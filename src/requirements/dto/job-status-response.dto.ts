import { ApiProperty } from '@nestjs/swagger';

export enum JobStatus {
  QUEUED = 'queued',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum JobStep {
  VALIDATING = 'validating',
  UPLOADING_TO_STORAGE = 'uploading_to_storage',
  PROCESSING_CONTENT = 'processing_content',
  SAVING_TO_DATABASE = 'saving_to_database',
  GENERATING_ANALYSIS = 'generating_analysis',
  COMPLETED = 'completed'
}

export class JobProgressDto {
  @ApiProperty({
    description: 'Current step being processed',
    enum: JobStep,
    example: JobStep.UPLOADING_TO_STORAGE
  })
  currentStep: JobStep;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 45
  })
  percentage: number;

  @ApiProperty({
    description: 'Human-readable description of current progress',
    example: 'Uploading file to cloud storage...'
  })
  description: string;

  @ApiProperty({
    description: 'Timestamp of last update',
    example: '2024-01-15T10:30:00Z'
  })
  updatedAt: Date;
}

export class JobStatusResponseDto {
  @ApiProperty({
    description: 'Job ID',
    example: 'upload_123e4567-e89b-12d3-a456-426614174000'
  })
  jobId: string;

  @ApiProperty({
    description: 'Current job status',
    enum: JobStatus,
    example: JobStatus.PROCESSING
  })
  status: JobStatus;

  @ApiProperty({
    description: 'Detailed progress information',
    type: JobProgressDto
  })
  progress: JobProgressDto;

  @ApiProperty({
    description: 'Job creation timestamp',
    example: '2024-01-15T10:25:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Job completion timestamp (if completed)',
    example: '2024-01-15T10:35:00Z',
    required: false
  })
  completedAt?: Date;

  @ApiProperty({
    description: 'Error message if job failed',
    required: false,
    example: 'File format not supported'
  })
  error?: string;

  @ApiProperty({
    description: 'Result data when job is completed',
    required: false
  })
  result?: {
    id: string;
    requirement_id: string;
    status: string;
    storageUrl: string;
  };
}
