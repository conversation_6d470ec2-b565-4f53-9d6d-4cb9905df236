import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, MaxLength, IsArray, IsNumber } from 'class-validator';

export enum EmbeddingProvider {
  OPENAI = 'openai',
  GEMINI = 'gemini'
}

export enum EmbeddingModel {
  // OpenAI models
  OPENAI_ADA_002 = 'text-embedding-ada-002',
  OPENAI_3_SMALL = 'text-embedding-3-small',
  OPENAI_3_LARGE = 'text-embedding-3-large',
  
  // Gemini models
  GEMINI_EMBEDDING = 'models/embedding-001'
}

export class GenerateEmbeddingDto {
  @ApiProperty({
    description: 'Text content to generate embedding for',
    example: 'User authentication requirements for the mobile application',
    maxLength: 8000
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(8000, { message: 'Text content cannot exceed 8000 characters' })
  text: string;

  @ApiProperty({
    description: 'Embedding provider to use',
    enum: EmbeddingProvider,
    example: EmbeddingProvider.OPENAI,
    required: false
  })
  @IsOptional()
  @IsEnum(EmbeddingProvider)
  provider?: EmbeddingProvider;

  @ApiProperty({
    description: 'Specific model to use for embedding generation',
    enum: EmbeddingModel,
    example: EmbeddingModel.OPENAI_ADA_002,
    required: false
  })
  @IsOptional()
  @IsEnum(EmbeddingModel)
  model?: EmbeddingModel;

  @ApiProperty({
    description: 'Optional context or metadata for the embedding',
    example: 'Requirements document for mobile app authentication',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  context?: string;

  @ApiProperty({
    description: 'Optional requirement ID to associate with this embedding',
    example: 'req-uuid-123',
    required: false
  })
  @IsOptional()
  @IsString()
  requirementId?: string;
}

export class EmbeddingResponseDto {
  @ApiProperty({
    description: 'Generated embedding vector',
    example: [0.1, 0.2, 0.3, -0.1, 0.5],
    type: [Number]
  })
  @IsArray()
  @IsNumber({}, { each: true })
  vector: number[];

  @ApiProperty({
    description: 'Number of dimensions in the vector',
    example: 1536
  })
  @IsNumber()
  dimensions: number;

  @ApiProperty({
    description: 'Model used for embedding generation',
    example: 'text-embedding-ada-002'
  })
  @IsString()
  model: string;

  @ApiProperty({
    description: 'Provider used for embedding generation',
    example: 'openai'
  })
  @IsString()
  provider: string;

  @ApiProperty({
    description: 'Number of tokens used for this embedding',
    example: 25
  })
  @IsNumber()
  tokensUsed: number;

  @ApiProperty({
    description: 'Cost of generating this embedding (if available)',
    example: 0.0001,
    required: false
  })
  @IsOptional()
  @IsNumber()
  cost?: number;

  @ApiProperty({
    description: 'Processing time in milliseconds',
    example: 150
  })
  @IsNumber()
  processingTimeMs: number;

  @ApiProperty({
    description: 'Timestamp when embedding was generated',
    example: '2024-01-15T10:30:00Z'
  })
  @IsString()
  timestamp: string;
}

export class SimilaritySearchDto {
  @ApiProperty({
    description: 'Text query to search for similar requirements',
    example: 'user authentication and login functionality',
    maxLength: 1000
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  query: string;

  @ApiProperty({
    description: 'Maximum number of similar requirements to return',
    example: 10,
    minimum: 1,
    maximum: 50,
    required: false
  })
  @IsOptional()
  @IsNumber()
  limit?: number = 10;

  @ApiProperty({
    description: 'Minimum similarity threshold (0-1)',
    example: 0.7,
    minimum: 0,
    maximum: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  threshold?: number = 0.7;

  @ApiProperty({
    description: 'Embedding provider to use for query embedding',
    enum: EmbeddingProvider,
    example: EmbeddingProvider.OPENAI,
    required: false
  })
  @IsOptional()
  @IsEnum(EmbeddingProvider)
  provider?: EmbeddingProvider;
}

export class SimilarityResultDto {
  @ApiProperty({
    description: 'Requirement information'
  })
  requirement: {
    id: string;
    requirementId: string;
    name: string;
    content: string;
    metadata?: Record<string, any>;
  };

  @ApiProperty({
    description: 'Similarity score (0-1, higher is more similar)',
    example: 0.85
  })
  @IsNumber()
  similarity: number;
}
