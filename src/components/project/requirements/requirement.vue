<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import { io, Socket } from 'socket.io-client';
import { useAuthStore } from '../../../stores/auth';
import RequirementsTable from './RequirementsTable.vue';
import RequirementsUploadModal from './RequirementsUploadModal.vue';
import RequirementsFilterModal from './RequirementsFilterModal.vue';
import ConfirmationModal from '../../common/ConfirmationModal.vue';
import { Requirement, UploadJob, JobStatusResponse, UploadJobResponse, JobStatus } from '../../../types';

const route = useRoute();
const authStore = useAuthStore();
const projectId = route.params.id as string;

const getUserData = () => {
  let userId = authStore.user?.id;
  let companyId = authStore.user?.companyId;
  return { userId, companyId };
};

const { userId, companyId } = getUserData();

// State
const requirements = ref<Requirement[]>([]);
const loading = ref(false);
const error = ref('');
const searchQuery = ref('');
const selectedRequirements = ref<string[]>([]);

// Modals
const showUploadModal = ref(false);
const showFilterModal = ref(false);
const showDeleteModal = ref(false);

// Upload job tracking
const uploadJobs = ref<Map<string, UploadJob>>(new Map());
const socket = ref<Socket | null>(null);

// Pagination
const totalItems = ref(0);
const currentPage = ref(1);
const itemsPerPage = ref(50);

// Sorting
const sortField = ref('createdAt');
const sortDirection = ref('desc');

// Filters
const filters = ref({
  status: [] as string[],
  fileType: [] as string[],
});

const statusOptions = ['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected'];
const fileTypeOptions = ['pdf', 'md', 'txt', 'doc', 'docx'];

// Computed
const filteredRequirements = computed(() => {
  let filtered = requirements.value;

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(req => 
      req.name.toLowerCase().includes(query) ||
      req.requirement_id.toLowerCase().includes(query) ||
      req.file_name?.toLowerCase().includes(query)
    );
  }

  return filtered;
});

const hasActiveFilters = computed(() => {
  return filters.value.status.length > 0 || 
         filters.value.fileType.length > 0 ||
         searchQuery.value.length > 0;
});

const activeUploadJobs = computed(() => {
  return Array.from(uploadJobs.value.values()).filter(job => 
    job.status === 'pending' || job.status === 'processing'
  );
});

// Methods
const fetchRequirements = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    const response = await axios.get(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements`, {
      params: {
        project_id: projectId,
        page: currentPage.value,
        limit: itemsPerPage.value,
        ...filters.value
      }
    });
    
    requirements.value = response.data.data || [];
    totalItems.value = response.data.total || 0;
  } catch (err) {
    console.error('Error fetching requirements:', err);
    error.value = 'Failed to load requirements';
  } finally {
    loading.value = false;
  }
};

const handleUpload = async (file: File, _onProgress?: (progress: number) => void) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('name', file.name.split('.')[0]);
  formData.append('user_id', userId || '');
  formData.append('company_id', companyId || '');
  formData.append('project_id', projectId);

  try {
    // Start async upload and get job ID
    const response = await axios.post(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/upload/async`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    const uploadResponse: UploadJobResponse = response.data;
    const jobId = uploadResponse.jobId;
    
    if (!jobId) {
      throw new Error('No job ID returned from upload');
    }

    // Track the upload job
    uploadJobs.value.set(jobId, {
      id: jobId,
      fileName: file.name,
      status: 'pending',
      progress: 0
    });

    // Subscribe to job updates via WebSocket
    if (socket.value) {
      socket.value.emit('subscribe-to-job', { jobId });
    }

    return jobId;
  } catch (err) {
    console.error('Error uploading requirement:', err);
    error.value = 'Failed to upload requirement';
    throw err;
  }
};

const handleDelete = async (requirement: Requirement) => {
  try {
    await axios.delete(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/${requirement.id}`);
    await fetchRequirements();
  } catch (err) {
    console.error('Error deleting requirement:', err);
    error.value = 'Failed to delete requirement';
  }
};

const handleBulkDelete = async () => {
  if (selectedRequirements.value.length === 0) return;

  try {
    await Promise.all(
      selectedRequirements.value.map(id =>
        axios.delete(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/${id}`)
      )
    );
    
    selectedRequirements.value = [];
    showDeleteModal.value = false;
    await fetchRequirements();
  } catch (err) {
    console.error('Error deleting requirements:', err);
    error.value = 'Failed to delete requirements';
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchRequirements();
};

const handleSort = (field: string) => {
  if (sortField.value === field) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortField.value = field;
    sortDirection.value = 'asc';
  }
  fetchRequirements();
};

const handleApplyFilters = (newFilters: any) => {
  filters.value = { ...newFilters };
  currentPage.value = 1;
  fetchRequirements();
  showFilterModal.value = false;
};

const clearFilters = () => {
  filters.value = {
    status: [],
    fileType: []
  };
  searchQuery.value = '';
  currentPage.value = 1;
  fetchRequirements();
};

const checkQueueStatus = () => {
  window.open('http://localhost:3002/queues/queue/upload-queue', '_blank');
};

// WebSocket setup
const initializeWebSocket = () => {
  socket.value = io(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/upload-progress`, {
    transports: ['websocket'],
    autoConnect: true
  });

  socket.value.on('connect', () => {
    console.log('Connected to upload progress WebSocket');
  });

  socket.value.on('job-status-update', (data: JobStatusResponse) => {
    const job = uploadJobs.value.get(data.jobId);
    if (job) {
      job.status = data.status === JobStatus.PROCESSING ? 'processing' : 
                   data.status === JobStatus.COMPLETED ? 'completed' :
                   data.status === JobStatus.FAILED ? 'failed' : 'pending';
      job.progress = data.progress.percentage;
      uploadJobs.value.set(data.jobId, job);
    }
  });

  socket.value.on('job-completed', (data: { jobId: string; result: any }) => {
    const job = uploadJobs.value.get(data.jobId);
    if (job) {
      job.status = 'completed';
      job.progress = 100;
      uploadJobs.value.set(data.jobId, job);
      
      // Refresh requirements list
      fetchRequirements();
      
      // Remove job after a delay
      setTimeout(() => {
        uploadJobs.value.delete(data.jobId);
      }, 5000);
    }
  });

  socket.value.on('job-failed', (data: { jobId: string; error: any }) => {
    const job = uploadJobs.value.get(data.jobId);
    if (job) {
      job.status = 'failed';
      job.error = data.error.message || 'Upload failed';
      uploadJobs.value.set(data.jobId, job);
    }
  });

  socket.value.on('disconnect', () => {
    console.log('Disconnected from upload progress WebSocket');
  });
};

// Lifecycle
onMounted(() => {
  fetchRequirements();
  initializeWebSocket();
});

onBeforeUnmount(() => {
  if (socket.value) {
    socket.value.disconnect();
  }
});
</script>

<template>
  <div class="requirements">
    <div class="requirements-header">
      <div class="header-left">
        <h2>Requirements</h2>
        <p class="header-subtitle">Manage project requirements and documentation</p>
      </div>
      
      <div class="header-actions">
        <button
          v-if="selectedRequirements.length > 0"
          class="action-button delete-button"
          @click="showDeleteModal = true"
        >
          Delete Selected ({{ selectedRequirements.length }})
        </button>

        <button
          class="action-button queue-button"
          @click="checkQueueStatus"
          title="View Upload Queue Status"
        >
          Queue Status
        </button>

        <button
          class="action-button upload-button"
          @click="showUploadModal = true"
        >
          Upload Requirements
        </button>
      </div>
    </div>

    <!-- Upload Progress Section -->
    <div v-if="activeUploadJobs.length > 0" class="upload-progress-section">
      <h3>Upload Progress</h3>
      <div class="upload-jobs">
        <div v-for="job in activeUploadJobs" :key="job.id" class="upload-job">
          <div class="job-info">
            <span class="job-filename">{{ job.fileName }}</span>
            <span class="job-status" :class="job.status">{{ job.status }}</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: job.progress + '%' }"></div>
          </div>
          <span class="progress-text">{{ job.progress }}%</span>
          <div v-if="job.error" class="job-error">{{ job.error }}</div>
        </div>
      </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="search-filter-section">
      <div class="search-container">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search requirements..."
          class="search-input"
          @input="fetchRequirements"
        />
        <button class="search-button">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="filter-actions">
        <button
          class="filter-button"
          @click="showFilterModal = true"
          :class="{ active: hasActiveFilters }"
        >
          Filter
          <span v-if="hasActiveFilters" class="filter-count">
            {{ (filters.status.length + filters.fileType.length + (searchQuery ? 1 : 0)) }}
          </span>
        </button>

        <button
          v-if="hasActiveFilters"
          class="clear-filters-button"
          @click="clearFilters"
        >
          Clear Filters
        </button>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="error-message">
      {{ error }}
      <button @click="error = ''" class="close-error">×</button>
    </div>

    <!-- Requirements Table -->
    <RequirementsTable
      :requirements="filteredRequirements"
      :loading="loading"
      :selected-requirements="selectedRequirements"
      :total-items="totalItems"
      :current-page="currentPage"
      :items-per-page="itemsPerPage"
      :sort-field="sortField"
      :sort-direction="sortDirection"
      @update:selected-requirements="selectedRequirements = $event"
      @delete="handleDelete"
      @page-change="handlePageChange"
      @sort="handleSort"
    />

    <!-- Upload Modal -->
    <RequirementsUploadModal
      :show="showUploadModal"
      @close="showUploadModal = false"
      @upload="handleUpload"
    />

    <!-- Filter Modal -->
    <RequirementsFilterModal
      v-model="showFilterModal"
      :status-options="statusOptions"
      :file-type-options="fileTypeOptions"
      :initial-filters="filters"
      @apply="handleApplyFilters"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete Requirements"
      :message="`Are you sure you want to delete ${selectedRequirements.length} requirement(s)? This action cannot be undone.`"
      @confirm="handleBulkDelete"
      @cancel="showDeleteModal = false"
    />
  </div>
</template>

<style scoped>
.requirements {
  padding: 24px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.requirements-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.header-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-button {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #ffffff;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.upload-button {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.upload-button:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

.delete-button {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.delete-button:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

.queue-button {
  background-color: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
}

.queue-button:hover {
  background-color: #7c3aed;
  border-color: #7c3aed;
}

/* Upload Progress Section */
.upload-progress-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.upload-progress-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.upload-jobs {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-job {
  padding: 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.job-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.job-filename {
  font-weight: 500;
  color: #1e293b;
}

.job-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.job-status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.job-status.processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.job-status.completed {
  background-color: #d1fae5;
  color: #065f46;
}

.job-status.failed {
  background-color: #fee2e2;
  color: #991b1b;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
}

.job-error {
  margin-top: 8px;
  padding: 8px;
  background-color: #fee2e2;
  color: #991b1b;
  border-radius: 4px;
  font-size: 12px;
}

/* Search and Filter Section */
.search-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: #ffffff;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
}

.search-button svg {
  width: 16px;
  height: 16px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.filter-button {
  position: relative;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #ffffff;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.filter-button.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.filter-count {
  margin-left: 4px;
  padding: 2px 6px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
}

.clear-filters-button {
  padding: 8px 12px;
  border: 1px solid #ef4444;
  border-radius: 6px;
  background-color: #ffffff;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-filters-button:hover {
  background-color: #ef4444;
  color: white;
}

/* Error Message */
.error-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 16px;
  background-color: #fee2e2;
  color: #991b1b;
  border-radius: 6px;
  border: 1px solid #fecaca;
}

.close-error {
  background: none;
  border: none;
  color: #991b1b;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-error:hover {
  background-color: rgba(153, 27, 27, 0.1);
  border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .requirements {
    padding: 16px;
  }

  .requirements-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .search-filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-container {
    max-width: none;
  }

  .filter-actions {
    justify-content: flex-start;
  }
}
</style>
