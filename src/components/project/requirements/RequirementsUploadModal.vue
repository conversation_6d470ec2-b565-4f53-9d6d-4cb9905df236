<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps<{
  show: boolean;
}>();

const emit = defineEmits<{
  'close': [];
  'upload': [file: File, onProgress?: (progress: number) => void];
}>();

const file = ref<File | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const isUploading = ref(false);
const uploadProgress = ref(0);
const error = ref('');

const allowedTypes = ['pdf', 'md', 'txt', 'doc', 'docx'];
const maxFileSize = 10 * 1024 * 1024; // 10MB

// Methods
const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    validateAndSetFile(input.files[0]);
  }
};

const validateAndSetFile = (selectedFile: File) => {
  error.value = '';
  
  // Check file size
  if (selectedFile.size > maxFileSize) {
    error.value = 'File size must be less than 10MB';
    return;
  }
  
  // Check file type
  const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
  if (!fileExtension || !allowedTypes.includes(fileExtension)) {
    error.value = `File type not supported. Allowed types: ${allowedTypes.join(', ')}`;
    return;
  }
  
  file.value = selectedFile;
};

const handleUpload = async () => {
  if (!file.value) {
    error.value = 'Please select a file to upload';
    return;
  }

  isUploading.value = true;
  uploadProgress.value = 0;
  error.value = '';

  try {
    // Progress callback for async upload
    const onProgress = (progress: number) => {
      uploadProgress.value = progress;
    };

    // Emit upload with progress callback
    emit('upload', file.value, onProgress);

    // Upload initiated successfully - show initial progress
    uploadProgress.value = 10;

    // Reset form after a short delay to show completion
    setTimeout(() => {
      file.value = null;
      isUploading.value = false;
      uploadProgress.value = 0;
      if (fileInput.value) {
        fileInput.value.value = '';
      }
      emit('close');
    }, 1000);

  } catch (err) {
    console.error('Upload error:', err);
    error.value = 'Failed to upload file. Please try again.';
    isUploading.value = false;
    uploadProgress.value = 0;
  }
};

const removeFile = () => {
  file.value = null;
  error.value = '';
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

const closeModal = () => {
  if (!isUploading.value) {
    file.value = null;
    error.value = '';
    uploadProgress.value = 0;
    if (fileInput.value) {
      fileInput.value.value = '';
    }
    emit('close');
  }
};

// Drag and drop handlers
const preventDefault = (e: Event) => {
  e.preventDefault();
  e.stopPropagation();
};

const handleDrop = (e: DragEvent) => {
  preventDefault(e);
  isDragging.value = false;
  
  if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
    validateAndSetFile(e.dataTransfer.files[0]);
    
    // Update file input
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(e.dataTransfer.files[0]);
    if (fileInput.value) {
      fileInput.value.files = dataTransfer.files;
    }
  }
};

const handleDragOver = (e: Event) => {
  preventDefault(e);
  isDragging.value = true;
};

const handleDragEnter = (e: Event) => {
  preventDefault(e);
  isDragging.value = true;
};

const handleDragLeave = (e: Event) => {
  preventDefault(e);
  isDragging.value = false;
};

// Lifecycle
onMounted(() => {
  document.addEventListener('dragover', preventDefault);
  document.addEventListener('drop', preventDefault);
});

onBeforeUnmount(() => {
  document.removeEventListener('dragover', preventDefault);
  document.removeEventListener('drop', preventDefault);
});

// Format file size
const formatFileSize = (bytes: number) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};
</script>

<template>
  <div v-if="show" class="modal-overlay" @click.self="closeModal">
    <div class="upload-modal">
      <div class="modal-header">
        <h3>Upload Requirements</h3>
        <button 
          class="close-button" 
          @click="closeModal"
          :disabled="isUploading"
        >
          ×
        </button>
      </div>

      <div class="modal-body">
        <div v-if="!isUploading">
          <!-- File Upload Area -->
          <div 
            class="file-upload-area"
            :class="{ 
              'drag-active': isDragging,
              'has-file': file,
              'has-error': error
            }"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
          >
            <div v-if="!file" class="upload-placeholder">
              <div class="upload-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path fill-rule="evenodd" d="M11.47 2.47a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1-1.06 1.06l-3.22-3.22V16.5a.75.75 0 0 1-1.5 0V4.81L8.03 8.03a.75.75 0 0 1-1.06-1.06l4.5-4.5ZM3 15.75a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z" clip-rule="evenodd" />
                </svg>
              </div>
              <h4>Drop your requirement file here</h4>
              <p>or <label for="file-input" class="file-select-link">click to select</label></p>
              <div class="file-info">
                <p>Supported formats: {{ allowedTypes.join(', ').toUpperCase() }}</p>
                <p>Maximum file size: 10MB</p>
              </div>
            </div>

            <div v-else class="file-preview">
              <div class="file-icon">
                📄
              </div>
              <div class="file-details">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ formatFileSize(file.size) }}</div>
                <div class="file-type">{{ file.name.split('.').pop()?.toUpperCase() }}</div>
              </div>
              <button 
                class="remove-file-btn"
                @click="removeFile"
                type="button"
              >
                ×
              </button>
            </div>

            <input
              ref="fileInput"
              id="file-input"
              type="file"
              :accept="allowedTypes.map(type => `.${type}`).join(',')"
              @change="handleFileChange"
              class="file-input"
            />
          </div>

          <!-- Error Message -->
          <div v-if="error" class="error-message">
            {{ error }}
          </div>

          <!-- Upload Instructions -->
          <div class="upload-instructions">
            <h4>Upload Instructions:</h4>
            <ul>
              <li>Select a requirement document in PDF, Markdown, Text, or Word format</li>
              <li>The system will automatically extract and process the requirements</li>
              <li>You can review and edit the extracted requirements after upload</li>
              <li>File will be assigned a unique requirement ID automatically</li>
            </ul>
          </div>
        </div>

        <!-- Upload Progress -->
        <div v-else class="upload-progress">
          <div class="progress-header">
            <h4>Uploading {{ file?.name }}...</h4>
            <span class="progress-percentage">{{ uploadProgress }}%</span>
          </div>
          
          <div class="progress-bar">
            <div 
              class="progress-fill"
              :style="{ width: `${uploadProgress}%` }"
            ></div>
          </div>
          
          <p class="progress-message">
            Please wait while we process your requirement document...
          </p>
        </div>
      </div>

      <div class="modal-footer">
        <button 
          class="cancel-button"
          @click="closeModal"
          :disabled="isUploading"
        >
          Cancel
        </button>
        <button 
          class="upload-button"
          @click="handleUpload"
          :disabled="!file || isUploading || !!error"
        >
          {{ isUploading ? 'Uploading...' : 'Upload Requirement' }}
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.upload-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &:hover:not(:disabled) {
      background-color: #f3f4f6;
      color: #374151;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.modal-body {
  padding: 24px;
}

.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  transition: all 0.2s;
  position: relative;

  &.drag-active {
    border-color: #e94560;
    background-color: #fef2f2;
  }

  &.has-error {
    border-color: #dc2626;
    background-color: #fef2f2;
  }

  &.has-file {
    border-color: #10b981;
    background-color: #f0fdf4;
  }
}

.upload-placeholder {
  .upload-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    color: #9ca3af;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #374151;
  }

  p {
    margin: 0 0 16px 0;
    color: #6b7280;
  }

  .file-select-link {
    color: #e94560;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #d63553;
    }
  }

  .file-info {
    p {
      font-size: 12px;
      color: #9ca3af;
      margin: 4px 0;
    }
  }
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 16px;
  text-align: left;

  .file-icon {
    font-size: 32px;
  }

  .file-details {
    flex: 1;

    .file-name {
      font-weight: 500;
      color: #1f2937;
      margin-bottom: 4px;
    }

    .file-size, .file-type {
      font-size: 12px;
      color: #6b7280;
    }
  }

  .remove-file-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #dc2626;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background-color: #fee2e2;
    }
  }
}

.file-input {
  display: none;
}

.error-message {
  background-color: #fee2e2;
  color: #dc2626;
  padding: 12px;
  border-radius: 6px;
  margin-top: 16px;
  font-size: 14px;
}

.upload-instructions {
  margin-top: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 6px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #374151;
  }

  ul {
    margin: 0;
    padding-left: 20px;
    color: #6b7280;
    font-size: 13px;

    li {
      margin-bottom: 4px;
    }
  }
}

.upload-progress {
  text-align: center;

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 16px;
      color: #374151;
    }

    .progress-percentage {
      font-weight: 600;
      color: #e94560;
    }
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 16px;

    .progress-fill {
      height: 100%;
      background-color: #e94560;
      transition: width 0.3s ease;
    }
  }

  .progress-message {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .cancel-button {
    background-color: white;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover:not(:disabled) {
      background-color: #f9fafb;
    }
  }

  .upload-button {
    background-color: #e94560;
    color: white;
    border: none;

    &:hover:not(:disabled) {
      background-color: #d63553;
    }
  }
}
</style>
