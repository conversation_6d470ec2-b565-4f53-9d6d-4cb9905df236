# Requirements Feature

This directory contains the Requirements management feature for the AgentQ application.

## Overview

The Requirements feature allows users to:
- Upload requirement documents (PDF, MD, TXT, DOC, DOCX)
- View requirements in a comprehensive table
- Search and filter requirements
- Manage requirement status workflow
- Delete individual or multiple requirements

## Components

### 1. Requirements.vue
Main component that orchestrates the requirements management functionality.

**Features:**
- Search functionality
- Filter management
- Bulk operations
- Pagination
- Status management

### 2. RequirementsTable.vue
Table component for displaying requirements data.

**Features:**
- Sortable columns
- Checkbox selection
- Status badges
- File information display
- Pagination controls
- Individual delete actions

### 3. RequirementsUploadModal.vue
Modal component for uploading requirement files.

**Features:**
- Drag and drop file upload
- File type validation (PDF, MD, TXT, DOC, DOCX)
- File size validation (max 10MB)
- Upload progress indicator
- Error handling

### 4. RequirementsFilterModal.vue
Modal component for filtering requirements.

**Features:**
- Status filtering
- File type filtering
- Filter preview
- Reset functionality

## Data Structure

### Requirement Interface
```typescript
interface Requirement {
  id: string;
  projectId: string;
  requirementId: string;
  name: string;
  status: 'draft' | 'reviewed_by_ai' | 'revised' | 'approved' | 'published' | 'rejected';
  description?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
  uploadedAt: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  reviewedBy?: string;
  reviewedAt?: string;
}
```

## Status Workflow

1. **Draft** - Initial status when requirement is uploaded
2. **Reviewed by AI** - AI has processed and reviewed the requirement
3. **Revised** - Requirement has been modified after review
4. **Approved** - Requirement has been approved for implementation
5. **Published** - Requirement is published and available for development
6. **Rejected** - Requirement has been rejected

## API Endpoints

The component expects the following API endpoints:

- `GET /api/projects/:projectId/requirements` - Fetch requirements
- `POST /api/projects/:projectId/requirements/upload/async` - Upload requirement file
- `DELETE /api/projects/:projectId/requirements/:requirementId` - Delete requirement
- `DELETE /api/projects/:projectId/requirements/bulk` - Bulk delete requirements

## Styling

The components use SCSS with a consistent design system:
- Primary color: #e94560
- Status-specific color coding
- Responsive design
- Hover states and transitions
- Loading states

## Usage

The Requirements feature is accessible via the project sidebar menu. Users can:

1. Navigate to a project
2. Click on "Requirements" in the sidebar
3. Upload new requirements using the "Upload Requirements" button
4. Search and filter requirements using the search bar and filter button
5. Select multiple requirements for bulk operations
6. View requirement details in the table

## File Upload Support

Supported file formats:
- PDF (.pdf)
- Markdown (.md)
- Text (.txt)
- Word Document (.doc, .docx)

Maximum file size: 10MB

## Integration

The Requirements feature integrates with:
- Vue Router for navigation
- Axios for API calls
- The existing project structure
- The sidebar navigation system

## Future Enhancements

Potential future improvements:
- Requirement editing functionality
- Version control for requirements
- Comments and collaboration features
- Integration with test case generation
- Export functionality
- Advanced search capabilities
