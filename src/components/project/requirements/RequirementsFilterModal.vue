<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  statusOptions: string[];
  fileTypeOptions: string[];
  initialFilters: {
    status: string[];
    fileType: string[];
  };
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'apply': [filters: { status: string[]; fileType: string[] }];
}>();

// Local copy of filters for the modal
const filters = ref({
  status: [...props.initialFilters.status],
  fileType: [...props.initialFilters.fileType],
});

// Watch for changes in initialFilters
watch(() => props.initialFilters, (newFilters) => {
  if (props.modelValue) return; // Don't update while modal is open

  filters.value = {
    status: [...newFilters.status],
    fileType: [...newFilters.fileType],
  };
}, { deep: true });

// Methods
const closeModal = () => {
  emit('update:modelValue', false);
};

const applyFilters = () => {
  emit('apply', {
    status: [...filters.value.status],
    fileType: [...filters.value.fileType],
  });
  closeModal();
};

const resetFilters = () => {
  filters.value = {
    status: [],
    fileType: [],
  };
};

const toggleStatusFilter = (status: string) => {
  const index = filters.value.status.indexOf(status);
  if (index > -1) {
    filters.value.status.splice(index, 1);
  } else {
    filters.value.status.push(status);
  }
};

const toggleFileTypeFilter = (fileType: string) => {
  const index = filters.value.fileType.indexOf(fileType);
  if (index > -1) {
    filters.value.fileType.splice(index, 1);
  } else {
    filters.value.fileType.push(fileType);
  }
};

const formatStatus = (status: string) => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getStatusClass = (status: string) => {
  const statusClasses = {
    'draft': 'status-draft',
    'reviewed_by_ai': 'status-reviewed',
    'revised': 'status-revised',
    'approved': 'status-approved',
    'published': 'status-published',
    'rejected': 'status-rejected'
  };
  return statusClasses[status as keyof typeof statusClasses] || 'status-draft';
};
</script>

<template>
  <div v-if="modelValue" class="modal-overlay" @click.self="closeModal">
    <div class="filter-modal">
      <div class="modal-header">
        <h3>Filter Requirements</h3>
        <button class="close-button" @click="closeModal">×</button>
      </div>

      <div class="modal-body">
        <div class="filter-grid">
          <!-- Status Filter -->
          <div class="filter-group">
            <h4>Status</h4>
            <div class="checkbox-group">
              <div
                v-for="status in statusOptions"
                :key="status"
                class="checkbox-item"
              >
                <label>
                  <input
                    type="checkbox"
                    :checked="filters.status.includes(status)"
                    @change="toggleStatusFilter(status)"
                  />
                  <span
                    class="status-badge small"
                    :class="getStatusClass(status)"
                  >
                    {{ formatStatus(status) }}
                  </span>
                </label>
              </div>
            </div>
          </div>

          <!-- File Type Filter -->
          <div class="filter-group">
            <h4>File Type</h4>
            <div class="checkbox-group">
              <div
                v-for="fileType in fileTypeOptions"
                :key="fileType"
                class="checkbox-item"
              >
                <label>
                  <input
                    type="checkbox"
                    :checked="filters.fileType.includes(fileType)"
                    @change="toggleFileTypeFilter(fileType)"
                  />
                  <span class="file-type-badge">
                    {{ fileType.toUpperCase() }}
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Filter Summary -->
        <div v-if="filters.status.length > 0 || filters.fileType.length > 0" class="filter-summary">
          <h4>Selected Filters:</h4>
          <div class="selected-filters">
            <div v-for="status in filters.status" :key="`status-${status}`" class="selected-filter">
              Status: {{ formatStatus(status) }}
            </div>
            <div v-for="fileType in filters.fileType" :key="`fileType-${fileType}`" class="selected-filter">
              File Type: {{ fileType.toUpperCase() }}
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="resetFilters" class="reset-button">Reset</button>
        <button @click="closeModal" class="cancel-button">Cancel</button>
        <button @click="applyFilters" class="apply-button">Apply Filters</button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.filter-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }
}

.modal-body {
  padding: 24px;
}

.filter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
}

.filter-group {
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .checkbox-item {
    label {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 4px 0;

      input[type="checkbox"] {
        margin: 0;
      }

      &:hover {
        .status-badge, .file-type-badge {
          opacity: 0.8;
        }
      }
    }
  }
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.small {
    padding: 2px 6px;
    font-size: 11px;
  }

  &.status-draft {
    background-color: #f3f4f6;
    color: #374151;
  }

  &.status-reviewed {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.status-revised {
    background-color: #fef3c7;
    color: #d97706;
  }

  &.status-approved {
    background-color: #d1fae5;
    color: #065f46;
  }

  &.status-published {
    background-color: #dcfce7;
    color: #166534;
  }

  &.status-rejected {
    background-color: #fee2e2;
    color: #dc2626;
  }
}

.file-type-badge {
  background-color: #f3f4f6;
  color: #374151;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  font-family: monospace;
}

.filter-summary {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }

  .selected-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .selected-filter {
      background-color: #e5e7eb;
      color: #374151;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .reset-button {
    background-color: white;
    color: #6b7280;
    border: 1px solid #d1d5db;

    &:hover {
      background-color: #f9fafb;
      color: #374151;
    }
  }

  .cancel-button {
    background-color: white;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background-color: #f9fafb;
    }
  }

  .apply-button {
    background-color: #e94560;
    color: white;
    border: none;

    &:hover {
      background-color: #d63553;
    }
  }
}
</style>
