<script setup lang="ts">
import { ref, computed } from 'vue';
import { Requirement } from '../../../types';

const props = defineProps<{
  requirements: Requirement[];
  loading: boolean;
  selectedRequirements: string[];
  totalItems: number;
  currentPage: number;
  itemsPerPage: number;
  sortField: string;
  sortDirection: string;
}>();

const emit = defineEmits<{
  'update:selectedRequirements': [value: string[]];
  'delete': [requirement: Requirement];
  'page-change': [page: number];
  'sort': [field: string];
}>();

const gotoPage = ref<number | null>(null);

// Computed
const totalPages = computed(() => Math.ceil(props.totalItems / props.itemsPerPage));

const isAllSelected = computed(() => {
  return props.requirements.length > 0 && 
         props.requirements.every(req => props.selectedRequirements.includes(req.id));
});

const isIndeterminate = computed(() => {
  const selectedCount = props.requirements.filter(req => 
    props.selectedRequirements.includes(req.id)
  ).length;
  return selectedCount > 0 && selectedCount < props.requirements.length;
});

// Methods
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // Deselect all current page items
    const currentPageIds = props.requirements.map(req => req.id);
    const newSelected = props.selectedRequirements.filter(id => !currentPageIds.includes(id));
    emit('update:selectedRequirements', newSelected);
  } else {
    // Select all current page items
    const currentPageIds = props.requirements.map(req => req.id);
    const newSelected = [...new Set([...props.selectedRequirements, ...currentPageIds])];
    emit('update:selectedRequirements', newSelected);
  }
};

const toggleSelect = (requirementId: string) => {
  const isSelected = props.selectedRequirements.includes(requirementId);
  if (isSelected) {
    emit('update:selectedRequirements', 
      props.selectedRequirements.filter(id => id !== requirementId)
    );
  } else {
    emit('update:selectedRequirements', [...props.selectedRequirements, requirementId]);
  }
};

const handleSort = (field: string) => {
  emit('sort', field);
};

const getSortIcon = (field: string) => {
  if (props.sortField !== field) return '↕️';
  return props.sortDirection === 'asc' ? '↑' : '↓';
};

const formatStatus = (status: string) => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getStatusClass = (status: string) => {
  const statusClasses = {
    'draft': 'status-draft',
    'reviewed_by_ai': 'status-reviewed',
    'revised': 'status-revised',
    'approved': 'status-approved',
    'published': 'status-published',
    'rejected': 'status-rejected'
  };
  return statusClasses[status as keyof typeof statusClasses] || 'status-draft';
};

const formatFileSize = (bytes?: number) => {
  if (!bytes) return '-';
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const goToPage = () => {
  if (gotoPage.value && gotoPage.value >= 1 && gotoPage.value <= totalPages.value) {
    emit('page-change', gotoPage.value);
    gotoPage.value = null;
  }
};
</script>

<template>
  <div class="requirements-table-container">
    <div class="table-wrapper">
      <table class="requirements-table">
        <thead>
          <tr>
            <th class="checkbox-column">
              <input
                type="checkbox"
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="toggleSelectAll"
              />
            </th>
            <th class="sortable" @click="handleSort('requirementId')">
              Requirement ID
              <span class="sort-icon">{{ getSortIcon('requirementId') }}</span>
            </th>
            <th class="sortable" @click="handleSort('name')">
              Name
              <span class="sort-icon">{{ getSortIcon('name') }}</span>
            </th>
            <th class="sortable" @click="handleSort('status')">
              Status
              <span class="sort-icon">{{ getSortIcon('status') }}</span>
            </th>
            <th>File Info</th>
            <th class="sortable" @click="handleSort('createdAt')">
              Created
              <span class="sort-icon">{{ getSortIcon('createdAt') }}</span>
            </th>
            <th class="actions-column">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading" class="loading-row">
            <td colspan="7" class="loading-cell">
              <div class="loading-spinner"></div>
              Loading requirements...
            </td>
          </tr>
          <tr v-else-if="requirements.length === 0" class="empty-row">
            <td colspan="7" class="empty-cell">
              No requirements found
            </td>
          </tr>
          <tr 
            v-else
            v-for="requirement in requirements" 
            :key="requirement.id"
            class="requirement-row"
            :class="{ selected: selectedRequirements.includes(requirement.id) }"
          >
            <td class="checkbox-column">
              <input
                type="checkbox"
                :checked="selectedRequirements.includes(requirement.id)"
                @change="toggleSelect(requirement.id)"
              />
            </td>
            <td class="requirement-id">
              {{ requirement.requirement_id }}
            </td>
            <td class="requirement-name">
              <div class="name-container">
                <span class="name">{{ requirement.name }}</span>
                <span v-if="requirement.description" class="description">
                  {{ requirement.description }}
                </span>
              </div>
            </td>
            <td class="status-column">
              <span class="status-badge" :class="getStatusClass(requirement.status)">
                {{ formatStatus(requirement.status) }}
              </span>
            </td>
            <td class="file-info">
              <div v-if="requirement.file_name" class="file-details">
                <div class="file-name">{{ requirement.file_name }}</div>
                <div class="file-meta">
                  <span class="file-type">{{ requirement.file_type?.toUpperCase() }}</span>
                  <span class="file-size">{{ formatFileSize(requirement.file_size) }}</span>
                </div>
              </div>
              <span v-else class="no-file">No file</span>
            </td>
            <td class="created-date">
              {{ formatDate(requirement.created_at) }}
            </td>
            <td class="actions-column">
              <button
                class="delete-btn"
                @click="emit('delete', requirement)"
                title="Delete requirement"
              >
                🗑️
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="pagination">
      <div class="pagination-info">
        Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to 
        {{ Math.min(currentPage * itemsPerPage, totalItems) }} of {{ totalItems }} requirements
      </div>
      
      <div class="pagination-controls">
        <button 
          :disabled="currentPage === 1"
          @click="emit('page-change', currentPage - 1)"
          class="pagination-btn"
        >
          Previous
        </button>
        
        <div class="page-numbers">
          <button
            v-for="page in Math.min(5, totalPages)"
            :key="page"
            :class="['page-btn', { active: page === currentPage }]"
            @click="emit('page-change', page)"
          >
            {{ page }}
          </button>
          
          <span v-if="totalPages > 5" class="page-ellipsis">...</span>
          
          <div class="goto-page">
            <input
              v-model.number="gotoPage"
              type="number"
              :min="1"
              :max="totalPages"
              placeholder="Go to"
              class="goto-input"
              @keyup.enter="goToPage"
            />
            <button @click="goToPage" class="goto-btn">Go</button>
          </div>
        </div>
        
        <button 
          :disabled="currentPage === totalPages"
          @click="emit('page-change', currentPage + 1)"
          class="pagination-btn"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.requirements-table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-wrapper {
  overflow-x: auto;
}

.requirements-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 14px;

    &.sortable {
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: #f3f4f6;
      }

      .sort-icon {
        margin-left: 4px;
        opacity: 0.5;
      }
    }
  }

  .checkbox-column {
    width: 40px;
  }

  .actions-column {
    width: 80px;
  }

  .requirement-row {
    &:hover {
      background-color: #f9fafb;
    }

    &.selected {
      background-color: #fef2f2;
    }
  }

  .requirement-id {
    font-family: monospace;
    font-weight: 600;
    color: #1f2937;
  }

  .name-container {
    .name {
      display: block;
      font-weight: 500;
      color: #1f2937;
      margin-bottom: 2px;
    }

    .description {
      display: block;
      font-size: 12px;
      color: #6b7280;
      line-height: 1.4;
    }
  }

  .status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;

    &.status-draft {
      background-color: #f3f4f6;
      color: #374151;
    }

    &.status-reviewed {
      background-color: #dbeafe;
      color: #1e40af;
    }

    &.status-revised {
      background-color: #fef3c7;
      color: #d97706;
    }

    &.status-approved {
      background-color: #d1fae5;
      color: #065f46;
    }

    &.status-published {
      background-color: #dcfce7;
      color: #166534;
    }

    &.status-rejected {
      background-color: #fee2e2;
      color: #dc2626;
    }
  }

  .file-details {
    .file-name {
      font-size: 13px;
      color: #1f2937;
      margin-bottom: 2px;
    }

    .file-meta {
      display: flex;
      gap: 8px;
      font-size: 11px;
      color: #6b7280;

      .file-type {
        background-color: #f3f4f6;
        padding: 1px 4px;
        border-radius: 2px;
      }
    }
  }

  .no-file {
    color: #9ca3af;
    font-style: italic;
    font-size: 13px;
  }

  .created-date {
    font-size: 13px;
    color: #6b7280;
  }

  .delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    font-size: 16px;

    &:hover {
      background-color: #fee2e2;
    }
  }

  .loading-cell, .empty-cell {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #e94560;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;

  .pagination-info {
    font-size: 14px;
    color: #6b7280;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pagination-btn {
    padding: 6px 12px;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;

    &:hover:not(:disabled) {
      background-color: #f9fafb;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-numbers {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .page-btn {
    padding: 6px 10px;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #f9fafb;
    }

    &.active {
      background-color: #e94560;
      color: white;
      border-color: #e94560;
    }
  }

  .goto-page {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;

    .goto-input {
      width: 60px;
      padding: 4px 6px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 12px;
    }

    .goto-btn {
      padding: 4px 8px;
      background-color: #e94560;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        background-color: #d63553;
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
