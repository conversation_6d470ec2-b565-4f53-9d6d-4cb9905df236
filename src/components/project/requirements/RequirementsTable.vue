<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import axios from 'axios';
import { Requirement } from '../../../types';

const props = defineProps<{
  requirements: Requirement[];
  loading: boolean;
  selectedRequirements: string[];
  totalItems: number;
  currentPage: number;
  itemsPerPage: number;
  sortField: string;
  sortDirection: string;
}>();

const emit = defineEmits<{
  'update:selectedRequirements': [value: string[]];
  'delete': [requirement: Requirement];
  'page-change': [page: number];
  'sort': [field: string];
  'update-status': [requirement: Requirement, action: string];
}>();

const gotoPage = ref<number | null>(null);
const openDropdownId = ref<string | null>(null);

// AI Analysis modal state
const showAiAnalysisModal = ref(false);
const aiAnalysisProgress = ref(0);
const currentAnalysisRequirement = ref<Requirement | null>(null);
const isAnalyzing = ref(false);

// Computed
const totalPages = computed(() => Math.ceil(props.totalItems / props.itemsPerPage));

const isAllSelected = computed(() => {
  return props.requirements.length > 0 && 
         props.requirements.every(req => props.selectedRequirements.includes(req.id));
});

const isIndeterminate = computed(() => {
  const selectedCount = props.requirements.filter(req => 
    props.selectedRequirements.includes(req.id)
  ).length;
  return selectedCount > 0 && selectedCount < props.requirements.length;
});

// Methods
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // Deselect all current page items
    const currentPageIds = props.requirements.map(req => req.id);
    const newSelected = props.selectedRequirements.filter(id => !currentPageIds.includes(id));
    emit('update:selectedRequirements', newSelected);
  } else {
    // Select all current page items
    const currentPageIds = props.requirements.map(req => req.id);
    const newSelected = [...new Set([...props.selectedRequirements, ...currentPageIds])];
    emit('update:selectedRequirements', newSelected);
  }
};

const toggleSelect = (requirementId: string) => {
  const isSelected = props.selectedRequirements.includes(requirementId);
  if (isSelected) {
    emit('update:selectedRequirements', 
      props.selectedRequirements.filter(id => id !== requirementId)
    );
  } else {
    emit('update:selectedRequirements', [...props.selectedRequirements, requirementId]);
  }
};

const handleSort = (field: string) => {
  emit('sort', field);
};

const getSortIcon = (field: string) => {
  if (props.sortField !== field) return '↕️';
  return props.sortDirection === 'asc' ? '↑' : '↓';
};

const formatStatus = (status: string) => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getStatusClass = (status: string) => {
  const statusClasses = {
    'draft': 'status-draft',
    'reviewed_by_ai': 'status-reviewed',
    'revised': 'status-revised',
    'approved': 'status-approved',
    'published': 'status-published',
    'rejected': 'status-rejected'
  };
  return statusClasses[status as keyof typeof statusClasses] || 'status-draft';
};

const formatFileSize = (bytes?: number) => {
  if (!bytes) return '-';
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const goToPage = () => {
  if (gotoPage.value && gotoPage.value >= 1 && gotoPage.value <= totalPages.value) {
    emit('page-change', gotoPage.value);
    gotoPage.value = null;
  }
};

// Dropdown methods
const toggleDropdown = (requirementId: string, event: Event) => {
  openDropdownId.value = openDropdownId.value === requirementId ? null : requirementId;

  if (openDropdownId.value) {
    // Calculate dropdown position
    const button = event.target as HTMLElement;
    const rect = button.getBoundingClientRect();

    // Store position for the dropdown
    setTimeout(() => {
      const dropdown = document.querySelector(`[data-dropdown-id="${requirementId}"]`) as HTMLElement;
      if (dropdown) {
        dropdown.style.position = 'fixed';
        dropdown.style.top = `${rect.bottom + 2}px`;
        dropdown.style.left = `${rect.right - 120}px`; // Align to right edge
        dropdown.style.zIndex = '1000';
      }
    }, 0);
  }
};

const closeDropdown = () => {
  openDropdownId.value = null;
};

const getDropdownOptions = (status: string) => {
  if (status === 'draft') {
    return [
      { label: 'AI Analysis', action: 'ai_analysis' },
      { label: 'Reject', action: 'reject' }
    ];
  }
  // Add more status-based options here as needed
  return [];
};

const validateApiKey = async (apiKey: string) => {
  try {
    const response = await axios.post(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile/validate-api-key`, {
      apiKey,
    });
    return response.data;
  } catch (err) {
    console.error('API key validation failed:', err);
    return { valid: false };
  }
};

const handleStatusUpdate = (requirement: Requirement, action: string) => {
  if (action === 'ai_analysis') {
    // Show AI analysis modal instead of immediate update
    startAiAnalysis(requirement);
  } else {
    // Handle other actions normally
    emit('update-status', requirement, action);
  }
  closeDropdown();
};

// AI Analysis methods
const startAiAnalysis = (requirement: Requirement) => {
  currentAnalysisRequirement.value = requirement;
  showAiAnalysisModal.value = true;
  isAnalyzing.value = true;
  aiAnalysisProgress.value = 0;

  // Start mock progress simulation
  simulateAiAnalysisProgress();
};

const simulateAiAnalysisProgress = () => {
  const steps = [
    { progress: 10, message: 'Initializing AI analysis...', delay: 500 },
    { progress: 25, message: 'Reading requirement document...', delay: 800 },
    { progress: 45, message: 'Analyzing content structure...', delay: 1000 },
    { progress: 65, message: 'Extracting key requirements...', delay: 1200 },
    { progress: 80, message: 'Validating completeness...', delay: 800 },
    { progress: 95, message: 'Generating analysis report...', delay: 600 },
    { progress: 100, message: 'Analysis complete!', delay: 500 }
  ];

  let currentStep = 0;

  const updateProgress = () => {
    if (currentStep < steps.length) {
      const step = steps[currentStep];
      aiAnalysisProgress.value = step.progress;

      if (step.progress === 100) {
        // Analysis complete
        setTimeout(() => {
          completeAiAnalysis();
        }, step.delay);
      } else {
        setTimeout(() => {
          currentStep++;
          updateProgress();
        }, step.delay);
      }
    }
  };

  updateProgress();
};

const completeAiAnalysis = () => {
  if (currentAnalysisRequirement.value) {
    // Emit the actual status update
    emit('update-status', currentAnalysisRequirement.value, 'ai_analysis');
  }

  // Close modal after a brief delay
  setTimeout(() => {
    showAiAnalysisModal.value = false;
    isAnalyzing.value = false;
    aiAnalysisProgress.value = 0;
    currentAnalysisRequirement.value = null;
  }, 1500);
};

const closeAiAnalysisModal = () => {
  if (!isAnalyzing.value) {
    showAiAnalysisModal.value = false;
    aiAnalysisProgress.value = 0;
    currentAnalysisRequirement.value = null;
  }
};

const getProgressMessage = () => {
  const progress = aiAnalysisProgress.value;
  if (progress === 0) return 'Preparing to analyze...';
  if (progress <= 10) return 'Initializing AI analysis...';
  if (progress <= 25) return 'Reading requirement document...';
  if (progress <= 45) return 'Analyzing content structure...';
  if (progress <= 65) return 'Extracting key requirements...';
  if (progress <= 80) return 'Validating completeness...';
  if (progress <= 95) return 'Generating analysis report...';
  if (progress === 100) return 'Analysis complete!';
  return 'Processing...';
};

// Click outside handler
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.dropdown-container')) {
    closeDropdown();
  }
};

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div class="requirements-table-container">
    <div class="table-wrapper">
      <table class="requirements-table">
        <thead>
          <tr>
            <th class="checkbox-column">
              <input
                type="checkbox"
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="toggleSelectAll"
              />
            </th>
            <th class="sortable" @click="handleSort('requirementId')">
              Requirement ID
              <span class="sort-icon">{{ getSortIcon('requirementId') }}</span>
            </th>
            <th class="sortable" @click="handleSort('name')">
              Name
              <span class="sort-icon">{{ getSortIcon('name') }}</span>
            </th>
            <th class="sortable" @click="handleSort('status')">
              Status
              <span class="sort-icon">{{ getSortIcon('status') }}</span>
            </th>
            <th>File Info</th>
            <th class="sortable" @click="handleSort('createdAt')">
              Created
              <span class="sort-icon">{{ getSortIcon('createdAt') }}</span>
            </th>
            <th class="actions-column">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading" class="loading-row">
            <td colspan="7" class="loading-cell">
              <div class="loading-spinner"></div>
              Loading requirements...
            </td>
          </tr>
          <tr v-else-if="requirements.length === 0" class="empty-row">
            <td colspan="7" class="empty-cell">
              No requirements found
            </td>
          </tr>
          <tr 
            v-else
            v-for="requirement in requirements" 
            :key="requirement.id"
            class="requirement-row"
            :class="{ selected: selectedRequirements.includes(requirement.id) }"
          >
            <td class="checkbox-column">
              <input
                type="checkbox"
                :checked="selectedRequirements.includes(requirement.id)"
                @change="toggleSelect(requirement.id)"
              />
            </td>
            <td class="requirement-id">
              {{ requirement.requirement_id }}
            </td>
            <td class="requirement-name">
              <div class="name-container">
                <span class="name">{{ requirement.name }}</span>
                <span v-if="requirement.description" class="description">
                  {{ requirement.description }}
                </span>
              </div>
            </td>
            <td class="status-column">
              <span class="status-badge" :class="getStatusClass(requirement.status)">
                {{ formatStatus(requirement.status) }}
              </span>
            </td>
            <td class="file-info">
              <div v-if="requirement.file_name" class="file-details">
                <div class="file-name">{{ requirement.file_name }}</div>
                <div class="file-meta">
                  <span class="file-type">{{ requirement.file_type?.toUpperCase() }}</span>
                  <span class="file-size">{{ formatFileSize(requirement.file_size) }}</span>
                </div>
              </div>
              <span v-else class="no-file">No file</span>
            </td>
            <td class="created-date">
              {{ formatDate(requirement.created_at) }}
            </td>
            <td class="actions-column">
              <div class="dropdown-container">
                <button
                  class="requirement-actions"
                  title="Requirement actions"
                  @click="toggleDropdown(requirement.id, $event)"
                >
                  Update ▼
                </button>

                <div
                  v-if="openDropdownId === requirement.id && getDropdownOptions(requirement.status).length > 0"
                  class="dropdown-menu"
                  :data-dropdown-id="requirement.id"
                >
                  <button
                    v-for="option in getDropdownOptions(requirement.status)"
                    :key="option.action"
                    class="dropdown-item"
                    @mousedown="handleStatusUpdate(requirement, option.action)"
                  >
                    {{ option.label }}
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="pagination">
      <div class="pagination-info">
        Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to 
        {{ Math.min(currentPage * itemsPerPage, totalItems) }} of {{ totalItems }} requirements
      </div>
      
      <div class="pagination-controls">
        <button 
          :disabled="currentPage === 1"
          @click="emit('page-change', currentPage - 1)"
          class="pagination-btn"
        >
          Previous
        </button>
        
        <div class="page-numbers">
          <button
            v-for="page in Math.min(5, totalPages)"
            :key="page"
            :class="['page-btn', { active: page === currentPage }]"
            @click="emit('page-change', page)"
          >
            {{ page }}
          </button>
          
          <span v-if="totalPages > 5" class="page-ellipsis">...</span>
          
          <div class="goto-page">
            <input
              v-model.number="gotoPage"
              type="number"
              :min="1"
              :max="totalPages"
              placeholder="Go to"
              class="goto-input"
              @keyup.enter="goToPage"
            />
            <button @click="goToPage" class="goto-btn">Go</button>
          </div>
        </div>
        
        <button 
          :disabled="currentPage === totalPages"
          @click="emit('page-change', currentPage + 1)"
          class="pagination-btn"
        >
          Next
        </button>
      </div>
    </div>

    <!-- AI Analysis Modal -->
    <div v-if="showAiAnalysisModal" class="modal-overlay" @click.self="closeAiAnalysisModal">
      <div class="ai-analysis-modal">
        <div class="modal-header">
          <h3>AI Analysis in Progress</h3>
          <button
            v-if="!isAnalyzing"
            class="close-button"
            @click="closeAiAnalysisModal"
          >
            ×
          </button>
        </div>

        <div class="modal-body">
          <div class="analysis-info">
            <h4>Analyzing: {{ currentAnalysisRequirement?.name }}</h4>
            <p class="requirement-id">ID: {{ currentAnalysisRequirement?.requirement_id }}</p>
          </div>

          <div class="progress-section">
            <div class="progress-header">
              <span class="progress-label">Analysis Progress</span>
              <span class="progress-percentage">{{ aiAnalysisProgress }}%</span>
            </div>

            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: `${aiAnalysisProgress}%` }"
              ></div>
            </div>

            <p class="progress-message">
              {{ getProgressMessage() }}
            </p>
          </div>

          <div v-if="aiAnalysisProgress === 100" class="completion-message">
            <div class="success-icon">✓</div>
            <p>AI analysis completed successfully!</p>
          </div>
        </div>

        <div class="modal-footer">
          <div v-if="isAnalyzing" class="analyzing-indicator">
            <div class="spinner"></div>
            Analyzing requirement...
          </div>
          <button
            v-else
            class="close-btn"
            @click="closeAiAnalysisModal"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.requirements-table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.table-wrapper {
  overflow-x: auto;
  overflow-y: visible;
}

.requirements-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 14px;

    &.sortable {
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: #f3f4f6;
      }

      .sort-icon {
        margin-left: 4px;
        opacity: 0.5;
      }
    }
  }

  .checkbox-column {
    width: 40px;
  }

  .actions-column {
    width: 120px;
    position: relative;
  }

  .requirement-row {
    &:hover {
      background-color: #f9fafb;
    }

    &.selected {
      background-color: #fef2f2;
    }
  }

  .requirement-id {
    font-family: monospace;
    font-weight: 600;
    color: #1f2937;
  }

  .name-container {
    .name {
      display: block;
      font-weight: 500;
      color: #1f2937;
      margin-bottom: 2px;
    }

    .description {
      display: block;
      font-size: 12px;
      color: #6b7280;
      line-height: 1.4;
    }
  }

  .status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;

    &.status-draft {
      background-color: #f3f4f6;
      color: #374151;
    }

    &.status-reviewed {
      background-color: #dbeafe;
      color: #1e40af;
    }

    &.status-revised {
      background-color: #fef3c7;
      color: #d97706;
    }

    &.status-approved {
      background-color: #d1fae5;
      color: #065f46;
    }

    &.status-published {
      background-color: #dcfce7;
      color: #166534;
    }

    &.status-rejected {
      background-color: #fee2e2;
      color: #dc2626;
    }
  }

  .file-details {
    .file-name {
      font-size: 13px;
      color: #1f2937;
      margin-bottom: 2px;
    }

    .file-meta {
      display: flex;
      gap: 8px;
      font-size: 11px;
      color: #6b7280;

      .file-type {
        background-color: #f3f4f6;
        padding: 1px 4px;
        border-radius: 2px;
      }
    }
  }

  .no-file {
    color: #9ca3af;
    font-style: italic;
    font-size: 13px;
  }

  .created-date {
    font-size: 13px;
    color: #6b7280;
  }

  .dropdown-container {
    position: relative;
    display: inline-block;
  }

  .requirement-actions {
    background-color: #e94560;
    color: white;
    border: none;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s;

    &:hover {
      background-color: #d63553;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.2);
    }
  }

  .dropdown-menu {
    position: fixed;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 1000;
    min-width: 120px;
    /* Position will be set dynamically via JavaScript */
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 8px 12px;
    text-align: left;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 13px;
    color: #374151;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f9fafb;
    }

    &:first-child {
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }

    &:last-child {
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f3f4f6;
    }
  }

  .loading-cell, .empty-cell {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #e94560;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;

  .pagination-info {
    font-size: 14px;
    color: #6b7280;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pagination-btn {
    padding: 6px 12px;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;

    &:hover:not(:disabled) {
      background-color: #f9fafb;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-numbers {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .page-btn {
    padding: 6px 10px;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #f9fafb;
    }

    &.active {
      background-color: #e94560;
      color: white;
      border-color: #e94560;
    }
  }

  .goto-page {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;

    .goto-input {
      width: 60px;
      padding: 4px 6px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 12px;
    }

    .goto-btn {
      padding: 4px 8px;
      background-color: #e94560;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        background-color: #d63553;
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// AI Analysis Modal Styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.ai-analysis-modal {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }
}

.modal-body {
  padding: 24px;
}

.analysis-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;

  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
  }

  .requirement-id {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
    font-family: monospace;
  }
}

.progress-section {
  margin-bottom: 24px;

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .progress-label {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
    }

    .progress-percentage {
      font-size: 14px;
      font-weight: 600;
      color: #e94560;
    }
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #e94560 0%, #f97316 100%);
      transition: width 0.5s ease;
    }
  }

  .progress-message {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    text-align: center;
  }
}

.completion-message {
  text-align: center;
  padding: 16px;
  background-color: #f0fdf4;
  border-radius: 8px;
  border: 1px solid #bbf7d0;

  .success-icon {
    font-size: 32px;
    color: #16a34a;
    margin-bottom: 8px;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #166534;
    font-weight: 500;
  }
}

.modal-footer {
  padding: 0 24px 24px 24px;
  display: flex;
  justify-content: center;

  .analyzing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;

    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #e5e7eb;
      border-radius: 50%;
      border-top-color: #e94560;
      animation: spin 1s linear infinite;
    }
  }

  .close-btn {
    padding: 8px 24px;
    background-color: #e94560;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #d63553;
    }
  }
}
</style>
