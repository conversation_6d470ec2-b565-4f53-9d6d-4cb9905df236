import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import DashboardView from '../views/DashboardView.vue'
import Project from '../components/dashboard/Project.vue'
import ProjectDetail from '../components/dashboard/ProjectDetail.vue'
import GenerateTestCase from '../components/project/test_cases/AIGenerateTestCase.vue'
import TestRunDetailWrapper from '../components/project/test_runs/TestRunDetailWrapper.vue'
import SystemHealth from '../components/dashboard/SystemHealth.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    {
      path: '/',
      component: DashboardView,
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          redirect: { name: 'projects' }
        },
        {
          path: 'projects',
          name: 'projects',
          component: Project
        },
        {
          path: 'system-health',
          name: 'system-health',
          component: SystemHealth,
          meta: { requiresAdmin: true }
        },
        {
          path: 'reports',
          name: 'reports',
          component: () => import('../components/reports/ReportDashboard.vue')
        },
        {
          path: 'projects/:id',
          name: 'project-detail',
          component: ProjectDetail,
          children: [
            {
              path: '',
              redirect: { name: 'project-test-cases' }
            },
            {
              path: 'test-cases',
              name: 'project-test-cases',
              component: () => import('../components/project/TestCases.vue'),
            },
            {
              path: 'test-cases/:tcId/automation',
              name: 'test-automation',
              component: () => import('../components/project/TestAutomation.vue')
            },
            {
              path: 'test-cases/:tcId/automation-security',
              name: 'test-automation-security',
              component: () => import('../components/project/TestAutomationSecurity.vue')
            },
            {
              path: 'test-runs',
              name: 'project-test-runs',
              component: () => import('../components/project/TestRuns.vue')
            },
            {
              path: 'test-runs/:testRunId',
              name: 'project-test-run-detail',
              component: TestRunDetailWrapper
            },
            {
              path: 'issues',
              name: 'project-issues',
              component: () => import('../components/project/Issues.vue')
            },
            {
              path: 'requirements',
              name: 'project-requirements',
              component: () => import('../components/project/Requirements.vue')
            },
            {
              path: 'generate',
              name: 'project-generate',
              component: GenerateTestCase,
              meta: { hideMainMenu: true }
            },
            {
              path: 'reports',
              name: 'project-reports',
              component: () => import('../components/reports/ReportDashboard.vue')
            }
          ]
        }
      ]
    },
    {
      path: '/auth/external',
      name: 'external-auth',
      component: () => import('../views/ExternalAuthView.vue')
    }
  ]
})

// Navigation guard for protected routes
router.beforeEach((to, _from, next) => {
  const isAuthenticated = !!localStorage.getItem('token')

  if (to.meta.requiresAuth && !isAuthenticated) {
    next({ name: 'login' })
  } else {
    next()
  }
})

export default router
