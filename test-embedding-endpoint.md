# Testing the Embedding Endpoint

## Quick Test Guide

### Prerequisites

1. **Start the server**:
   ```bash
   npm run start:dev
   ```

2. **Get JWT token** (replace with your authentication method):
   ```bash
   # Login to get token
   curl -X POST "http://localhost:3000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"email": "your-email", "password": "your-password"}'
   ```

3. **Set environment variables**:
   ```bash
   export JWT_TOKEN="your-jwt-token-here"
   export BASE_URL="http://localhost:3000"
   ```

### Test 1: Generate Simple Embedding

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "The system shall authenticate users using OAuth 2.0 protocol"
  }' | jq '.'
```

**Expected Response**:
```json
{
  "vector": [0.1, 0.2, 0.3, ...], // 1536 numbers
  "dimensions": 1536,
  "model": "text-embedding-ada-002",
  "provider": "openai",
  "tokensUsed": 12,
  "cost": 0.0000012,
  "processingTimeMs": 150,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Test 2: Generate Embedding with Tracking

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "User authentication requirements for mobile application",
    "context": "Mobile app security requirements",
    "requirementId": "req-test-123",
    "provider": "openai",
    "model": "text-embedding-ada-002"
  }' | jq '.'
```

### Test 3: Semantic Search

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding/search" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "user login and authentication",
    "limit": 5,
    "threshold": 0.7
  }' | jq '.'
```

**Expected Response**:
```json
[
  {
    "requirement": {
      "id": "req-uuid-1",
      "requirementId": "R-001",
      "name": "User Authentication",
      "content": "The system shall authenticate users...",
      "metadata": {}
    },
    "similarity": 0.85
  }
]
```

### Test 4: Get Available Models

```bash
curl -X GET "${BASE_URL}/api/v1/requirements/embedding/models" \
  -H "Authorization: Bearer ${JWT_TOKEN}" | jq '.'
```

### Test 5: Test Different Providers

#### OpenAI Provider
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Security requirements for data encryption",
    "provider": "openai",
    "model": "text-embedding-ada-002"
  }' | jq '.provider, .model, .dimensions'
```

#### Gemini Provider (if configured)
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Security requirements for data encryption",
    "provider": "gemini",
    "model": "models/embedding-001"
  }' | jq '.provider, .model, .dimensions'
```

## Advanced Testing

### Test 6: Large Text Content

```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "The system shall implement a comprehensive user authentication mechanism that supports multiple authentication methods including username/password, OAuth 2.0, SAML, and multi-factor authentication. The authentication system must be secure, scalable, and provide seamless user experience across all platforms including web, mobile, and desktop applications. Security requirements include password complexity rules, account lockout policies, session management, and audit logging of all authentication events."
  }' | jq '.tokensUsed, .cost, .processingTimeMs'
```

### Test 7: Error Handling

#### Test with text too long
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "'$(python3 -c "print('A' * 9000)")"'"
  }'
```

#### Test without authentication
```bash
curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Test without auth"
  }'
```

### Test 8: Performance Testing

```bash
# Test multiple requests in parallel
for i in {1..5}; do
  curl -X POST "${BASE_URL}/api/v1/requirements/embedding" \
    -H "Authorization: Bearer ${JWT_TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{
      \"text\": \"Performance test request number ${i} with unique content\",
      \"context\": \"Performance testing batch ${i}\"
    }" &
done
wait
```

## Validation Checklist

### ✅ **Basic Functionality**
- [ ] Embedding generation works
- [ ] Vector has correct dimensions (1536 for OpenAI, 768 for Gemini)
- [ ] Token usage is calculated
- [ ] Cost is computed correctly
- [ ] Processing time is reasonable (<500ms)

### ✅ **Authentication & Security**
- [ ] JWT token is required
- [ ] Invalid tokens are rejected
- [ ] Input validation works (text length limits)
- [ ] SQL injection protection

### ✅ **Provider Support**
- [ ] OpenAI provider works (if API key configured)
- [ ] Gemini provider works (if API key configured)
- [ ] Fallback behavior on provider failure
- [ ] Model selection works

### ✅ **Search Functionality**
- [ ] Semantic search returns relevant results
- [ ] Similarity scores are reasonable (0-1 range)
- [ ] Threshold filtering works
- [ ] Limit parameter works

### ✅ **Error Handling**
- [ ] Graceful handling of API failures
- [ ] Proper error messages
- [ ] HTTP status codes are correct
- [ ] No sensitive data in error responses

### ✅ **Performance**
- [ ] Response times are acceptable
- [ ] Concurrent requests work
- [ ] Memory usage is stable
- [ ] No memory leaks

## Database Verification

### Check Token Usage Tracking

```sql
-- Check if token usage is being tracked
SELECT 
  requirement_id,
  token_type,
  tokens_used,
  model,
  provider,
  cost,
  created_at
FROM token_usage 
WHERE requirement_id = 'req-test-123'
ORDER BY created_at DESC;
```

### Check Vector Storage

```sql
-- Check if embeddings are stored properly
SELECT 
  id,
  requirement_id,
  name,
  CASE 
    WHEN embedding_vector IS NOT NULL THEN 'Has Vector'
    ELSE 'No Vector'
  END as vector_status,
  metadata->>'embeddingStatus' as embedding_status
FROM requirements 
WHERE id = 'your-requirement-id';
```

## Troubleshooting

### Common Issues

1. **"Failed to generate embedding"**
   - Check API keys in environment variables
   - Verify provider service is available
   - Check network connectivity

2. **"Unauthorized"**
   - Verify JWT token is valid and not expired
   - Check token format (Bearer prefix)
   - Ensure user has proper permissions

3. **"Text content cannot exceed 8000 characters"**
   - Reduce input text length
   - Split large content into chunks
   - Use summarization before embedding

4. **Empty search results**
   - Check if requirements have embeddings
   - Lower similarity threshold
   - Verify vector data exists in database

### Debug Commands

```bash
# Check server logs
docker logs your-app-container -f

# Check database connection
psql -h localhost -U your_user -d requirement_service -c "SELECT COUNT(*) FROM requirements;"

# Check Redis connection (for queues)
redis-cli ping

# Check environment variables
env | grep -E "(OPENAI|GEMINI|EMBEDDING)"
```

## Next Steps

After successful testing:

1. **Integrate with frontend** - Add embedding search to your UI
2. **Implement caching** - Cache frequently used embeddings
3. **Add batch processing** - Process multiple texts at once
4. **Monitor costs** - Set up alerts for high token usage
5. **Optimize performance** - Tune vector indexes and queries

The embedding endpoint is now ready for production use with comprehensive vector search capabilities! 🚀
